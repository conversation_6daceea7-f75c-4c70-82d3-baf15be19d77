import { <PERSON><PERSON>, <PERSON><PERSON>, Typography } from "@mui/material"
import { useState } from "react"
import AlertSnackbar from "@/components/AlertSnackBar"
import { getFunctions, httpsCallable } from "firebase/functions"

export default function FormHistory() {
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })
  const [isLoading, setIsLoading] = useState(false)

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  const handleActivateFormHistory = async () => {
    setIsLoading(true)
    
    try {
      const functions = getFunctions()
      const setupFormChangeTracking = httpsCallable(functions, 'setupFormChangeTracking')
      
      const result = await setupFormChangeTracking()
      console.log('Setup result:', result.data)
      
      setAlertInfo({
        open: true,
        message: "Form change tracking activated successfully",
        severity: "success"
      })
    } catch (error) {
      console.error('Setup failed:', error)
      setAlertInfo({
        open: true,
        message: `Error activating form history: ${error.message || "Unknown error"}`,
        severity: "error"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Stack direction="column" alignItems="center" sx={{ padding: "50px 0" }}>
      <Typography variant="h6" sx={{ marginBottom: "30px", textAlign: "center" }}>
        Activate Form History Tracking
      </Typography>
      <Typography variant="body2" sx={{ marginBottom: "40px", textAlign: "center", maxWidth: "400px" }}>
        This experimental feature will enable tracking of form changes across your organization.
      </Typography>
      
      <Button 
        variant="contained" 
        color="primary" 
        onClick={handleActivateFormHistory}
        disabled={isLoading}
      >
        {isLoading ? "Activating..." : "Activate Form History"}
      </Button>

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Stack>
  )
}
