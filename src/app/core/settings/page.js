"use client"

import ProjectSettings from "./ProjectSettings"
import PhaseSettings from "./PhaseSettings"
import FormHistorySettings from "./FormHistorySettings"
import { Box, Typography, Card, Tabs, Tab } from "@mui/material"
import { useState } from "react"
import { useProjectTypeList } from "@/helpers/firebaseGetMethods"

function Settings() {
  const [selectedTab, setSelectedTab] = useState(0)

  const tabs = {
    0: <ProjectSettings />,
    1: <PhaseSettings />,
    2: <FormHistorySettings />
  }

  const selectTab = (_e, newTab) => {
    setSelectedTab(newTab)
  }

  useProjectTypeList()

  return (
    <Box
      sx={{
        bgcolor: "#F3f3f3",
        display: "flex",
        flexWrap: "wrap",
        alignContent: "center",
        flexDirection: "column",
        paddingTop: "100px",
        height: "calc(100vh - 64px)"
      }}
    >
      <Typography variant="h3">Settings</Typography>
      <Card
        sx={{
          marginTop: "50px",
          marginBottom: "16px",
          width: "75%",
          boxShadow: "4px 6px 6px #aaa",
          padding: "25px"
        }}
      >
        <Tabs value={selectedTab} onChange={selectTab}>
          <Tab label="Project Settings" />
          <Tab label="Phase Settings" />
          <Tab label="Form History" />
        </Tabs>
        {tabs[selectedTab]}
      </Card>
    </Box>
  )
}

export default Settings
