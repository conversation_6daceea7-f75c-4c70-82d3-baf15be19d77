import { useState } from "react"
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Stack
} from "@mui/material"
import ExpandMoreIcon from "@mui/icons-material/ExpandMore"
import { useQueryClient } from "@tanstack/react-query"
import { getFunctions, httpsCallable } from "firebase/functions"
import AlertSnackbar from "@/components/AlertSnackBar"

const FormHistorySettings = () => {
  const [expandedAccordion, setExpandedAccordion] = useState(false)
  const [formStructures, setFormStructures] = useState({})
  const [loadingStructures, setLoadingStructures] = useState({})
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })

  const queryClient = useQueryClient()
  const projectTypeList = queryClient.getQueryData(["projectTypeList"]) || []

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  const handleAccordionChange = async (panel, projectTypeId) => {
    const isExpanding = expandedAccordion !== panel

    setExpandedAccordion(isExpanding ? panel : false)

    // If expanding and we don't have the form structure yet, fetch it
    if (isExpanding && !formStructures[projectTypeId]) {
      setLoadingStructures((prev) => ({ ...prev, [projectTypeId]: true }))

      try {
        const functions = getFunctions()
        const getProjectTypeFormStructure = httpsCallable(functions, 'getProjectTypeFormStructure')
        
        const result = await getProjectTypeFormStructure({ projectTypeId })
        
        setFormStructures((prev) => ({
          ...prev,
          [projectTypeId]: result.data
        }))
      } catch (error) {
        console.error('Failed to fetch form structure:', error)
        setAlertInfo({
          open: true,
          message: `Error loading form structure: ${error.message || "Unknown error"}`,
          severity: "error"
        })
      } finally {
        setLoadingStructures((prev) => ({ ...prev, [projectTypeId]: false }))
      }
    }
  }

  const renderFormStructure = (structure) => {
    if (!structure) return null

    // Handle different possible structure formats
    if (typeof structure === 'string') {
      return (
        <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
          {structure}
        </Typography>
      )
    }

    if (typeof structure === 'object') {
      return (
        <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
          {JSON.stringify(structure, null, 2)}
        </Typography>
      )
    }

    return (
      <Typography variant="body2">
        {String(structure)}
      </Typography>
    )
  }

  return (
    <Box sx={{ margin: "50px" }}>
      <Typography variant="h4" sx={{ marginBottom: "30px" }}>
        Form History
      </Typography>
      
      <Typography variant="body1" sx={{ marginBottom: "30px", color: "text.secondary" }}>
        View form structures for each project type. Click on a project type to expand and see its form structure.
      </Typography>

      {projectTypeList.length === 0 ? (
        <Typography variant="body1" sx={{ textAlign: "center", padding: "40px" }}>
          No project types available.
        </Typography>
      ) : (
        <Stack spacing={1}>
          {projectTypeList.map((projectType) => {
            const projectTypeId = projectType.projectTypeId.native
            const panelId = `panel-${projectTypeId}`
            const isLoading = loadingStructures[projectTypeId]
            const formStructure = formStructures[projectTypeId]

            return (
              <Accordion
                key={projectTypeId}
                expanded={expandedAccordion === panelId}
                onChange={() => handleAccordionChange(panelId, projectTypeId)}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls={`${panelId}-content`}
                  id={`${panelId}-header`}
                >
                  <Typography variant="h6">
                    {projectType.name}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {isLoading ? (
                    <Stack direction="row" alignItems="center" spacing={2} sx={{ padding: "20px" }}>
                      <CircularProgress size={24} />
                      <Typography variant="body2">
                        Loading form structure...
                      </Typography>
                    </Stack>
                  ) : formStructure ? (
                    <Box sx={{ padding: "10px 0" }}>
                      {renderFormStructure(formStructure)}
                    </Box>
                  ) : (
                    <Typography variant="body2" sx={{ fontStyle: "italic", color: "text.secondary" }}>
                      Click to load form structure for this project type.
                    </Typography>
                  )}
                </AccordionDetails>
              </Accordion>
            )
          })}
        </Stack>
      )}

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Box>
  )
}

export default FormHistorySettings
